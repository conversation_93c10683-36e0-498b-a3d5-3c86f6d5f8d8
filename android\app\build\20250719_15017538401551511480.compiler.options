"-Xallow-no-source-files" "-classpath" "D:\\20223\\2025\\legl92025\\android\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\release\\processReleaseResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\41837628685453a74245b2f1c557f13e\\transformed\\jetified-libs.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cloud_functions-5.6.1\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80916d37d20235830b20236efd7826b9\\transformed\\jetified-kotlin-stdlib-jdk7-1.9.10.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cloud_firestore-5.6.11\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\connectivity_plus-6.1.4\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-11.5.0\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-8.3.7\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_auth-5.6.2\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_database-11.3.9\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_messaging-15.2.9\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_storage-12.4.9\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core-3.15.1\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-17.2.4\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.28\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_sign_in_android-6.2.1\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_android-0.8.12+23\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus-8.3.0\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_android-13.0.1\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\syncfusion_flutter_pdfviewer-30.1.39\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\783d0154b6ba25a1ac999661f8a8d53b\\transformed\\jetified-flutter_embedding_release-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09b2178e937d73cd70c3a7e99a2a1b4b\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbe7c81b9c7cc590d579a954982dca4\\transformed\\jetified-activity-1.9.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1e6b8baab410f687cb656e199c0da25\\transformed\\loader-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\833c4854612ea6d3db1fe7aeab2f0e06\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\18de149db502245b4da0973d3d04d11b\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9aff8d1ae3ced87883a1cf5ec31cdb28\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8302962c826767b4ee56e50a1ad179fd\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dda58f0cf9ff254d1f1eb2c4dda0e8d5\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4597f77c0e2e2e798faa9783e0b4fef0\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e0ba29245b3c8fba241037a825112f01\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f10d37196801d27c3020c1db9970297\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fbea15a95e1c315197b098bccaab79e4\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d2e871e0c9f75e88d6aff906b8b18030\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a42db3ac0c6b89f4d10a29fcfb3cfdb8\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e7b9663e003374e0c2311327d5c62249\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e43638bf2dc655a7b5df01c074e8b0ab\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8f6b489dd3852be3a551d39b7ef3d6aa\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8df4a5ff265e582216e44db391630d78\\transformed\\jetified-annotation-jvm-1.9.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\68853f3c8c535f82f2db49e3889aa0ee\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bf358c4aae14182db307a8ec070a19c5\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\da0614cc94f485823ec117117a642885\\transformed\\jetified-kotlin-stdlib-1.9.24.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3ef81e61ed5ea56d8712480a3d897ef\\transformed\\jetified-kotlin-stdlib-jdk8-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6b1aa933cef8ed2bca9a5e37b33a77e4\\transformed\\jetified-armeabi_v7a_release-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\895000a65de895673f52162d4a75156f\\transformed\\jetified-arm64_v8a_release-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\680a8f2d0fc53acc2e79d97920ac879f\\transformed\\jetified-x86_64_release-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee708a9f0630f8a50894e69c4af3e047\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ec8625db22f20aa05739d48d9e35c398\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be6e24b8cfc8b2e7675505f5c4d5aea2\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19ddca5fc21c151b36cb2f6d2012bd64\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\20223\\2025\\legl92025\\android\\app\\build\\tmp\\kotlin-classes\\release" "-jvm-target" "11" "-module-name" "app_release" "-no-jdk" "-no-reflect" "-no-stdlib" "D:\\20223\\2025\\legl92025\\android\\app\\src\\main\\java\\io\\flutter\\plugins\\GeneratedPluginRegistrant.java" "D:\\20223\\2025\\legl92025\\android\\app\\src\\main\\kotlin\\com\\legal2025\\yamy\\MainActivity.kt"