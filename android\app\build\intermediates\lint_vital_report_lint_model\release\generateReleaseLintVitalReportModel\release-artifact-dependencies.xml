<dependencies>
  <compile
      roots="__local_aars__:D:\20223\2025\legl92025\android\app\build\intermediates\flutter\release\libs.jar:unspecified@jar,:@@:cloud_functions::release,:@@:shared_preferences_android::release,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,:@@:cloud_firestore::release,:@@:connectivity_plus::release,:@@:device_info_plus::release,:@@:file_picker::release,:@@:firebase_auth::release,:@@:firebase_database::release,:@@:firebase_messaging::release,:@@:firebase_storage::release,:@@:firebase_core::release,:@@:flutter_local_notifications::release,:@@:flutter_plugin_android_lifecycle::release,:@@:google_sign_in_android::release,:@@:image_picker_android::release,:@@:package_info_plus::release,:@@:path_provider_android::release,:@@:permission_handler_android::release,:@@:syncfusion_flutter_pdfviewer::release,:@@:url_launcher_android::release,io.flutter:flutter_embedding_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,androidx.fragment:fragment:1.7.1@aar,androidx.activity:activity:1.9.3@aar,androidx.loader:loader:1.1.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.window:window:1.2.0@aar,androidx.window:window-java:1.2.0@aar,androidx.annotation:annotation-experimental:1.4.0@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,io.flutter:armeabi_v7a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,io.flutter:arm64_v8a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,io.flutter:x86_64_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,org.jetbrains:annotations:23.0.0@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,com.getkeepsafe.relinker:relinker:1.4.5@aar">
    <dependency
        name="__local_aars__:D:\20223\2025\legl92025\android\app\build\intermediates\flutter\release\libs.jar:unspecified@jar"
        simpleName="__local_aars__:D:\20223\2025\legl92025\android\app\build\intermediates\flutter\release\libs.jar"/>
    <dependency
        name=":@@:cloud_functions::release"
        simpleName="artifacts::cloud_functions"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="artifacts::shared_preferences_android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name=":@@:cloud_firestore::release"
        simpleName="artifacts::cloud_firestore"/>
    <dependency
        name=":@@:connectivity_plus::release"
        simpleName="artifacts::connectivity_plus"/>
    <dependency
        name=":@@:device_info_plus::release"
        simpleName="artifacts::device_info_plus"/>
    <dependency
        name=":@@:file_picker::release"
        simpleName="artifacts::file_picker"/>
    <dependency
        name=":@@:firebase_auth::release"
        simpleName="artifacts::firebase_auth"/>
    <dependency
        name=":@@:firebase_database::release"
        simpleName="artifacts::firebase_database"/>
    <dependency
        name=":@@:firebase_messaging::release"
        simpleName="artifacts::firebase_messaging"/>
    <dependency
        name=":@@:firebase_storage::release"
        simpleName="artifacts::firebase_storage"/>
    <dependency
        name=":@@:firebase_core::release"
        simpleName="artifacts::firebase_core"/>
    <dependency
        name=":@@:flutter_local_notifications::release"
        simpleName="artifacts::flutter_local_notifications"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="artifacts::flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:google_sign_in_android::release"
        simpleName="artifacts::google_sign_in_android"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="artifacts::image_picker_android"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="artifacts::package_info_plus"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="artifacts::path_provider_android"/>
    <dependency
        name=":@@:permission_handler_android::release"
        simpleName="artifacts::permission_handler_android"/>
    <dependency
        name=":@@:syncfusion_flutter_pdfviewer::release"
        simpleName="artifacts::syncfusion_flutter_pdfviewer"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="artifacts::url_launcher_android"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
  </compile>
  <package
      roots="__local_aars__:D:\20223\2025\legl92025\android\app\build\intermediates\flutter\release\libs.jar:unspecified@jar,:@@:cloud_functions::release,:@@:shared_preferences_android::release,:@@:firebase_auth::release,:@@:cloud_firestore::release,:@@:firebase_database::release,:@@:firebase_messaging::release,:@@:firebase_storage::release,:@@:firebase_core::release,com.google.firebase:firebase-auth:23.2.1@aar,com.google.firebase:firebase-database:21.0.0@aar,com.google.firebase:firebase-firestore:25.1.4@aar,com.google.firebase:firebase-functions:21.2.1@aar,com.google.firebase:firebase-messaging:24.1.2@aar,com.google.firebase:firebase-storage:21.0.2@aar,com.google.firebase:firebase-iid:21.1.0@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-appcheck:18.0.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,:@@:syncfusion_flutter_pdfviewer::release,:@@:device_info_plus::release,:@@:package_info_plus::release,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,:@@:file_picker::release,:@@:flutter_local_notifications::release,:@@:image_picker_android::release,:@@:url_launcher_android::release,:@@:connectivity_plus::release,:@@:flutter_plugin_android_lifecycle::release,:@@:google_sign_in_android::release,:@@:path_provider_android::release,:@@:permission_handler_android::release,io.flutter:flutter_embedding_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,androidx.media:media:1.1.0@aar,androidx.credentials:credentials:1.2.0-rc01@aar,androidx.credentials:credentials:1.2.0-rc01@aar,androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar,com.google.android.gms:play-services-auth:21.0.0@aar,androidx.preference:preference:1.2.1@aar,androidx.appcompat:appcompat:1.1.0@aar,com.google.firebase:firebase-auth-interop:20.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.recaptcha:recaptcha:18.6.1@aar,com.google.android.play:integrity:1.3.0@aar,com.google.firebase:firebase-appcheck-interop:17.1.0@aar,com.google.firebase:firebase-database-collection:18.0.1@aar,com.google.android.gms:play-services-fido:20.1.0@aar,com.google.android.gms:play-services-base:18.1.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.android.gms:play-services-cloud-messaging:17.2.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.recyclerview:recyclerview:1.0.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.1.0@aar,androidx.fragment:fragment-ktx:1.7.1@aar,androidx.activity:activity-ktx:1.9.3@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.window:window-java:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.window:window:1.2.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar,androidx.datastore:datastore-preferences-proto:1.1.3@jar,androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar,androidx.datastore:datastore-core-okio-jvm:1.1.3@jar,androidx.datastore:datastore-core-android:1.1.3@aar,androidx.datastore:datastore-preferences-android:1.1.3@aar,androidx.datastore:datastore-android:1.1.3@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.7.1@aar,androidx.fragment:fragment:1.7.1@aar,androidx.activity:activity:1.9.3@aar,androidx.browser:browser:1.8.0@aar,androidx.browser:browser:1.8.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.appcompat:appcompat-resources:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.transition:transition:1.4.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.exifinterface:exifinterface:1.3.7@aar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.1.0@jar,androidx.collection:collection:1.1.0@jar,com.google.firebase:firebase-components:18.0.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,com.google.firebase:firebase-datatransport:18.2.0@aar,com.google.android.datatransport:transport-backend-cct:3.1.9@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.android.datatransport:transport-runtime:3.1.9@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.window.extensions.core:core:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.annotation:annotation-experimental:1.4.0@aar,com.google.android.libraries.identity.googleid:googleid:1.1.0@aar,io.grpc:grpc-okhttp:1.62.2@jar,com.squareup.okhttp3:okhttp:3.12.13@jar,com.squareup.okio:okio-jvm:3.4.0@jar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,io.flutter:armeabi_v7a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,io.flutter:arm64_v8a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,io.flutter:x86_64_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,org.reactivestreams:reactive-streams:1.0.4@jar,io.grpc:grpc-android:1.62.2@aar,io.grpc:grpc-util:1.62.2@jar,io.grpc:grpc-core:1.62.2@jar,com.google.code.gson:gson:2.10.1@jar,org.jetbrains:annotations:23.0.0@jar,com.getkeepsafe.relinker:relinker:1.4.5@aar,io.grpc:grpc-protobuf-lite:1.62.2@jar,io.grpc:grpc-stub:1.62.2@jar,io.grpc:grpc-context:1.62.2@jar,io.grpc:grpc-api:1.62.2@jar,com.google.guava:guava:32.1.3-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,com.google.firebase:firebase-annotations:16.2.0@jar,com.google.firebase:protolite-well-known-types:18.0.1@aar,javax.inject:javax.inject:1@jar,com.google.errorprone:error_prone_annotations:2.26.0@jar,com.google.android.play:core-common:2.0.3@aar,com.google.protobuf:protobuf-javalite:3.25.5@jar,io.perfmark:perfmark-api:0.26.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.android:annotations:4.1.1.4@jar,org.codehaus.mojo:animal-sniffer-annotations:1.23@jar,com.google.guava:failureaccess:1.0.1@jar,org.checkerframework:checker-qual:3.37.0@jar">
    <dependency
        name="__local_aars__:D:\20223\2025\legl92025\android\app\build\intermediates\flutter\release\libs.jar:unspecified@jar"
        simpleName="__local_aars__:D:\20223\2025\legl92025\android\app\build\intermediates\flutter\release\libs.jar"/>
    <dependency
        name=":@@:cloud_functions::release"
        simpleName="artifacts::cloud_functions"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="artifacts::shared_preferences_android"/>
    <dependency
        name=":@@:firebase_auth::release"
        simpleName="artifacts::firebase_auth"/>
    <dependency
        name=":@@:cloud_firestore::release"
        simpleName="artifacts::cloud_firestore"/>
    <dependency
        name=":@@:firebase_database::release"
        simpleName="artifacts::firebase_database"/>
    <dependency
        name=":@@:firebase_messaging::release"
        simpleName="artifacts::firebase_messaging"/>
    <dependency
        name=":@@:firebase_storage::release"
        simpleName="artifacts::firebase_storage"/>
    <dependency
        name=":@@:firebase_core::release"
        simpleName="artifacts::firebase_core"/>
    <dependency
        name="com.google.firebase:firebase-auth:23.2.1@aar"
        simpleName="com.google.firebase:firebase-auth"/>
    <dependency
        name="com.google.firebase:firebase-database:21.0.0@aar"
        simpleName="com.google.firebase:firebase-database"/>
    <dependency
        name="com.google.firebase:firebase-firestore:25.1.4@aar"
        simpleName="com.google.firebase:firebase-firestore"/>
    <dependency
        name="com.google.firebase:firebase-functions:21.2.1@aar"
        simpleName="com.google.firebase:firebase-functions"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.1.2@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.firebase:firebase-storage:21.0.2@aar"
        simpleName="com.google.firebase:firebase-storage"/>
    <dependency
        name="com.google.firebase:firebase-iid:21.1.0@aar"
        simpleName="com.google.firebase:firebase-iid"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-appcheck:18.0.0@aar"
        simpleName="com.google.firebase:firebase-appcheck"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name=":@@:syncfusion_flutter_pdfviewer::release"
        simpleName="artifacts::syncfusion_flutter_pdfviewer"/>
    <dependency
        name=":@@:device_info_plus::release"
        simpleName="artifacts::device_info_plus"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="artifacts::package_info_plus"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name=":@@:file_picker::release"
        simpleName="artifacts::file_picker"/>
    <dependency
        name=":@@:flutter_local_notifications::release"
        simpleName="artifacts::flutter_local_notifications"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="artifacts::image_picker_android"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="artifacts::url_launcher_android"/>
    <dependency
        name=":@@:connectivity_plus::release"
        simpleName="artifacts::connectivity_plus"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="artifacts::flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:google_sign_in_android::release"
        simpleName="artifacts::google_sign_in_android"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="artifacts::path_provider_android"/>
    <dependency
        name=":@@:permission_handler_android::release"
        simpleName="artifacts::permission_handler_android"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.media:media:1.1.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.credentials:credentials:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials"/>
    <dependency
        name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials-play-services-auth"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.0.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="androidx.preference:preference:1.2.1@aar"
        simpleName="androidx.preference:preference"/>
    <dependency
        name="androidx.appcompat:appcompat:1.1.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
        simpleName="com.google.firebase:firebase-auth-interop"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.recaptcha:recaptcha:18.6.1@aar"
        simpleName="com.google.android.recaptcha:recaptcha"/>
    <dependency
        name="com.google.android.play:integrity:1.3.0@aar"
        simpleName="com.google.android.play:integrity"/>
    <dependency
        name="com.google.firebase:firebase-appcheck-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-appcheck-interop"/>
    <dependency
        name="com.google.firebase:firebase-database-collection:18.0.1@aar"
        simpleName="com.google.firebase:firebase-database-collection"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.0.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.7.1@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity-ktx:1.9.3@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-external-protobuf"/>
    <dependency
        name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-proto"/>
    <dependency
        name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-core-jvm"/>
    <dependency
        name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-core-okio-jvm"/>
    <dependency
        name="androidx.datastore:datastore-core-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-core-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-preferences-android"/>
    <dependency
        name="androidx.datastore:datastore-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.1.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.transition:transition:1.4.1@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.7@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.2.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.window.extensions.core:core:1.0.0@aar"
        simpleName="androidx.window.extensions.core:core"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
        simpleName="com.google.android.libraries.identity.googleid:googleid"/>
    <dependency
        name="io.grpc:grpc-okhttp:1.62.2@jar"
        simpleName="io.grpc:grpc-okhttp"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:3.12.13@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.4.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="org.reactivestreams:reactive-streams:1.0.4@jar"
        simpleName="org.reactivestreams:reactive-streams"/>
    <dependency
        name="io.grpc:grpc-android:1.62.2@aar"
        simpleName="io.grpc:grpc-android"/>
    <dependency
        name="io.grpc:grpc-util:1.62.2@jar"
        simpleName="io.grpc:grpc-util"/>
    <dependency
        name="io.grpc:grpc-core:1.62.2@jar"
        simpleName="io.grpc:grpc-core"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
    <dependency
        name="io.grpc:grpc-protobuf-lite:1.62.2@jar"
        simpleName="io.grpc:grpc-protobuf-lite"/>
    <dependency
        name="io.grpc:grpc-stub:1.62.2@jar"
        simpleName="io.grpc:grpc-stub"/>
    <dependency
        name="io.grpc:grpc-context:1.62.2@jar"
        simpleName="io.grpc:grpc-context"/>
    <dependency
        name="io.grpc:grpc-api:1.62.2@jar"
        simpleName="io.grpc:grpc-api"/>
    <dependency
        name="com.google.guava:guava:32.1.3-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="com.google.firebase:protolite-well-known-types:18.0.1@aar"
        simpleName="com.google.firebase:protolite-well-known-types"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.android.play:core-common:2.0.3@aar"
        simpleName="com.google.android.play:core-common"/>
    <dependency
        name="com.google.protobuf:protobuf-javalite:3.25.5@jar"
        simpleName="com.google.protobuf:protobuf-javalite"/>
    <dependency
        name="io.perfmark:perfmark-api:0.26.0@jar"
        simpleName="io.perfmark:perfmark-api"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.android:annotations:4.1.1.4@jar"
        simpleName="com.google.android:annotations"/>
    <dependency
        name="org.codehaus.mojo:animal-sniffer-annotations:1.23@jar"
        simpleName="org.codehaus.mojo:animal-sniffer-annotations"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="org.checkerframework:checker-qual:3.37.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
  </package>
</dependencies>
