# قواعد ProGuard مبسطة وآمنة

# Flutter الأساسي
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# Firebase (مبسط)
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# Syncfusion PDF Viewer
-keep class com.syncfusion.** { *; }

# المكونات الأساسية فقط
-keep class io.flutter.plugins.** { *; }
-keep class com.baseflow.** { *; }
-keep class dev.fluttercommunity.** { *; }
-keep class com.dexterous.** { *; }

# قواعد أساسية آمنة
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service

# الحفاظ على Parcelable
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}
