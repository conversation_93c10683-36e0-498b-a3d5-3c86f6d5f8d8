import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:flutter/foundation.dart';

/// عارض PDF للملفات المحملة محلياً - أداء فائق وميزات متقدمة
class LocalPDFViewer extends StatefulWidget {
  final String pdfPath;
  final String fileName;
  final String title;

  const LocalPDFViewer({
    super.key,
    required this.pdfPath,
    required this.fileName,
    required this.title,
  });

  @override
  State<LocalPDFViewer> createState() => _LocalPDFViewerState();
}

class _LocalPDFViewerState extends State<LocalPDFViewer>
    with TickerProviderStateMixin {
  late PdfViewerController _pdfController;
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsAnimation;

  bool _hasError = false;
  bool _isLoading = true;
  bool _showControls = true;
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;

  @override
  void initState() {
    super.initState();
    _pdfController = PdfViewerController();
    _setupAnimations();
    _initializeViewer();
  }

  void _setupAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));
    _controlsAnimationController.forward();
  }

  void _initializeViewer() {
    // التحقق من وجود الملف
    final file = File(widget.pdfPath);
    if (!file.existsSync()) {
      setState(() {
        _hasError = true;
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _isLoading = false;
    });

    if (kDebugMode) {
      print('📁 عارض PDF محلي - ملف جاهز للعرض');
      print('📄 المسار: ${widget.pdfPath}');
    }
  }

  @override
  void dispose() {
    _controlsAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Stack(
          children: [
            // عارض PDF الرئيسي
            _buildMainPDFViewer(),
            
            // أدوات التحكم العلوية
            if (_showControls) _buildTopControls(),
            
            // أدوات التحكم السفلية
            if (_showControls) _buildBottomControls(),
            
            // شاشة الخطأ
            if (_hasError) _buildErrorScreen(),
            
            // شاشة التحميل
            if (_isLoading) _buildLoadingScreen(),
          ],
        ),
      ),
    );
  }

  Widget _buildMainPDFViewer() {
    return GestureDetector(
      onTap: _toggleControls,
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: _buildPDFContent(),
      ),
    );
  }

  Widget _buildPDFContent() {
    return SfPdfViewer.file(
      File(widget.pdfPath),
      controller: _pdfController,
      // إعدادات محسنة للملفات المحلية - أداء فائق
      enableDoubleTapZooming: true,
      enableTextSelection: true, // تفعيل للملفات المحلية
      canShowScrollHead: true,
      canShowScrollStatus: true,
      canShowPaginationDialog: true,
      pageLayoutMode: PdfPageLayoutMode.continuous,
      scrollDirection: PdfScrollDirection.vertical,
      pageSpacing: 4,
      initialZoomLevel: 1.0,
      // تحسينات خاصة بالملفات المحلية
      interactionMode: PdfInteractionMode.selection,
      onDocumentLoaded: _onDocumentLoaded,
      onDocumentLoadFailed: _onDocumentLoadFailed,
      onPageChanged: _onPageChanged,
      onZoomLevelChanged: _onZoomLevelChanged,
      onTextSelectionChanged: _onTextSelectionChanged,
    );
  }

  void _onDocumentLoaded(PdfDocumentLoadedDetails details) {
    if (mounted) {
      setState(() {
        _totalPages = details.document.pages.count;
        _isLoading = false;
        _hasError = false;
      });
    }

    if (kDebugMode) {
      print('✅ ملف PDF محلي جاهز: ${details.document.pages.count} صفحة');
      print('📊 حجم الملف: ${_getFileSize()} MB');
    }
  }

  void _onDocumentLoadFailed(PdfDocumentLoadFailedDetails details) {
    if (mounted) {
      setState(() {
        _hasError = true;
        _isLoading = false;
      });
    }

    if (kDebugMode) {
      print('❌ خطأ في تحميل PDF المحلي: ${details.error}');
    }
  }

  void _onPageChanged(PdfPageChangedDetails details) {
    if (mounted) {
      setState(() {
        _currentPage = details.newPageNumber;
      });
    }
  }

  void _onZoomLevelChanged(PdfZoomDetails details) {
    if (mounted) {
      setState(() {
        _zoomLevel = details.newZoomLevel;
      });
    }
  }

  void _onTextSelectionChanged(PdfTextSelectionChangedDetails details) {
    if (kDebugMode && details.selectedText != null) {
      print('📝 تم تحديد نص: ${details.selectedText}');
    }
  }

  String _getFileSize() {
    try {
      final file = File(widget.pdfPath);
      final bytes = file.lengthSync();
      final mb = bytes / (1024 * 1024);
      return mb.toStringAsFixed(2);
    } catch (e) {
      return 'غير معروف';
    }
  }

  Widget _buildTopControls() {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Transform.translate(
            offset: Offset(0, -50 * (1 - _controlsAnimation.value)),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.8),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          widget.title,
                          style: GoogleFonts.cairo(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'ملف محلي • ${_getFileSize()} MB',
                          style: GoogleFonts.cairo(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: _shareFile,
                    icon: const Icon(Icons.share, color: Colors.white),
                  ),
                  IconButton(
                    onPressed: _showFileInfo,
                    icon: const Icon(Icons.info_outline, color: Colors.white),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomControls() {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Transform.translate(
            offset: Offset(0, 80 * (1 - _controlsAnimation.value)),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.8),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildControlButton(
                    icon: Icons.zoom_out,
                    onPressed: _zoomOut,
                    enabled: _zoomLevel > 0.5,
                  ),
                  _buildControlButton(
                    icon: Icons.navigate_before,
                    onPressed: _previousPage,
                    enabled: _currentPage > 1,
                  ),
                  _buildControlButton(
                    icon: Icons.search,
                    onPressed: _showSearchDialog,
                    enabled: true,
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '$_currentPage / $_totalPages',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  _buildControlButton(
                    icon: Icons.text_fields,
                    onPressed: _toggleTextSelection,
                    enabled: true,
                  ),
                  _buildControlButton(
                    icon: Icons.navigate_next,
                    onPressed: _nextPage,
                    enabled: _currentPage < _totalPages,
                  ),
                  _buildControlButton(
                    icon: Icons.zoom_in,
                    onPressed: _zoomIn,
                    enabled: _zoomLevel < 3.0,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required bool enabled,
  }) {
    return IconButton(
      onPressed: enabled ? onPressed : null,
      icon: Icon(
        icon,
        color: enabled ? Colors.white : Colors.white.withValues(alpha: 0.5),
        size: 20,
      ),
    );
  }

  Widget _buildErrorScreen() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'خطأ في فتح الملف المحلي',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.red[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'تأكد من وجود الملف وصحة المسار',
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'العودة',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'جاري فتح الملف المحلي',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.blue[800],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    
    if (_showControls) {
      _controlsAnimationController.forward();
      // إخفاء تلقائي بعد 5 ثوان للملفات المحلية
      Future.delayed(const Duration(seconds: 5), () {
        if (mounted && _showControls) {
          setState(() {
            _showControls = false;
          });
          _controlsAnimationController.reverse();
        }
      });
    } else {
      _controlsAnimationController.reverse();
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      _pdfController.previousPage();
    }
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      _pdfController.nextPage();
    }
  }

  void _zoomIn() {
    if (_zoomLevel < 3.0) {
      _pdfController.zoomLevel = (_zoomLevel + 0.25).clamp(0.5, 3.0);
    }
  }

  void _zoomOut() {
    if (_zoomLevel > 0.5) {
      _pdfController.zoomLevel = (_zoomLevel - 0.25).clamp(0.5, 3.0);
    }
  }

  void _shareFile() {
    if (kDebugMode) {
      print('مشاركة الملف المحلي: ${widget.fileName}');
    }
    // تنفيذ مشاركة الملف المحلي
  }

  void _showFileInfo() {
    final file = File(widget.pdfPath);
    final stat = file.statSync();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('معلومات الملف', style: GoogleFonts.cairo()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الاسم: ${widget.fileName}', style: GoogleFonts.cairo()),
            Text('الحجم: ${_getFileSize()} MB', style: GoogleFonts.cairo()),
            Text('الصفحات: $_totalPages', style: GoogleFonts.cairo()),
            Text('تاريخ التعديل: ${stat.modified}', style: GoogleFonts.cairo()),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    if (kDebugMode) {
      print('فتح نافذة البحث في النص');
    }
    // تنفيذ البحث في النص
  }

  void _toggleTextSelection() {
    if (kDebugMode) {
      print('تبديل وضع تحديد النص');
    }
    // تبديل وضع تحديد النص
  }
}
