import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

/// خدمة تحميل PDF محسنة للملفات الكبيرة
class EnhancedPdfLoader {
  static const Duration _timeout = Duration(seconds: 30);

  /// تحميل PDF مع التحميل التدريجي المحسن
  static Future<PdfLoadResult> loadPdf({
    required String url,
    required Function(double progress) onProgress,
    CancelToken? cancelToken,
  }) async {
    try {
      // تحويل رابط Google Drive إلى رابط مباشر
      final directUrl = _convertToDirectDownloadUrl(url);

      // التحقق من التخزين المؤقت أولاً مع فحص صحة الملف
      final cachedFile = await _getCachedFile(directUrl);
      if (cachedFile != null && await cachedFile.exists()) {
        final fileSize = await cachedFile.length();
        // التأكد من أن الملف ليس فارغاً أو تالفاً
        if (fileSize > 1024) {
          // على الأقل 1KB
          onProgress(1.0);
          return PdfLoadResult.success(cachedFile.path);
        } else {
          // حذف الملف التالف
          await cachedFile.delete();
        }
      }

      // بدء التحميل التدريجي
      return await _downloadWithProgress(directUrl, onProgress, cancelToken);
    } catch (e) {
      return PdfLoadResult.error(_getErrorMessage(e));
    }
  }

  /// تحميل مع عرض التقدم
  static Future<PdfLoadResult> _downloadWithProgress(
    String url,
    Function(double progress) onProgress,
    CancelToken? cancelToken,
  ) async {
    http.Client? client;
    IOSink? sink;
    File? tempFile;

    try {
      client = http.Client();

      // إنشاء ملف مؤقت
      final tempDir = await getTemporaryDirectory();
      tempFile = File(
        '${tempDir.path}/temp_pdf_${DateTime.now().millisecondsSinceEpoch}.pdf',
      );
      sink = tempFile.openWrite();

      // إرسال طلب HEAD للحصول على حجم الملف
      final headResponse = await client.head(Uri.parse(url)).timeout(_timeout);
      final contentLength =
          int.tryParse(headResponse.headers['content-length'] ?? '0') ?? 0;

      if (kDebugMode) {
        print('📊 حجم الملف: ${_formatFileSize(contentLength)}');
      }

      // بدء التحميل التدريجي
      final request = http.Request('GET', Uri.parse(url));
      request.headers.addAll({
        'User-Agent': 'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36',
        'Accept': 'application/pdf,*/*',
        'Connection': 'keep-alive',
      });

      final streamedResponse = await client.send(request).timeout(_timeout);

      if (streamedResponse.statusCode != 200) {
        throw Exception(
          'HTTP ${streamedResponse.statusCode}: ${streamedResponse.reasonPhrase}',
        );
      }

      int downloadedBytes = 0;
      final completer = Completer<PdfLoadResult>();

      // الاستماع للبيانات الواردة
      streamedResponse.stream.listen(
        (List<int> chunk) {
          // التحقق من الإلغاء
          if (cancelToken?.isCancelled == true) {
            sink?.close();
            tempFile?.delete();
            completer.complete(PdfLoadResult.cancelled());
            return;
          }

          // كتابة البيانات
          sink?.add(chunk);
          downloadedBytes += chunk.length;

          // تحديث التقدم
          if (contentLength > 0) {
            final progress = downloadedBytes / contentLength;
            onProgress(progress.clamp(0.0, 1.0));
          }
        },
        onDone: () async {
          await sink?.close();

          // التحقق من اكتمال التحميل
          if (await tempFile!.exists()) {
            final fileSize = await tempFile.length();
            if (fileSize > 0) {
              // نسخ إلى التخزين المؤقت
              final cachedFile = await _getCachedFile(url);
              if (cachedFile != null) {
                await tempFile.copy(cachedFile.path);
                await tempFile.delete();
                completer.complete(PdfLoadResult.success(cachedFile.path));
              } else {
                completer.complete(PdfLoadResult.success(tempFile.path));
              }
            } else {
              completer.complete(PdfLoadResult.error('الملف المحمل فارغ'));
            }
          } else {
            completer.complete(PdfLoadResult.error('فشل في إنشاء الملف'));
          }
        },
        onError: (error) {
          sink?.close();
          tempFile?.delete();
          completer.complete(PdfLoadResult.error(_getErrorMessage(error)));
        },
        cancelOnError: true,
      );

      return await completer.future;
    } catch (e) {
      await sink?.close();
      await tempFile?.delete();
      return PdfLoadResult.error(_getErrorMessage(e));
    } finally {
      client?.close();
    }
  }

  /// الحصول على ملف مخزن مؤقتاً
  static Future<File?> _getCachedFile(String url) async {
    try {
      final directory = await getTemporaryDirectory();
      final fileName = 'cached_${url.hashCode}.pdf';
      return File('${directory.path}/$fileName');
    } catch (e) {
      return null;
    }
  }

  /// تحويل رابط Google Drive إلى رابط مباشر
  static String _convertToDirectDownloadUrl(String url) {
    if (url.contains('drive.google.com') && url.contains('/file/d/')) {
      final fileIdMatch = RegExp(r'/file/d/([a-zA-Z0-9_-]+)').firstMatch(url);
      if (fileIdMatch != null) {
        final fileId = fileIdMatch.group(1);
        return 'https://drive.google.com/uc?export=download&id=$fileId';
      }
    }
    return url;
  }

  /// تنسيق حجم الملف
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// الحصول على رسالة خطأ مفهومة
  static String _getErrorMessage(dynamic error) {
    final errorStr = error.toString();

    if (errorStr.contains('SocketException')) {
      return 'لا يوجد اتصال بالإنترنت';
    } else if (errorStr.contains('TimeoutException')) {
      return 'انتهت مهلة التحميل - الملف كبير أو الاتصال بطيء';
    } else if (errorStr.contains('HttpException')) {
      return 'خطأ في الخادم';
    } else if (errorStr.contains('FormatException')) {
      return 'رابط غير صحيح';
    } else {
      return 'فشل في تحميل الملف: $errorStr';
    }
  }

  /// مسح التخزين المؤقت مع إدارة ذكية للذاكرة
  static Future<void> clearCache({bool smartCleanup = true}) async {
    try {
      final directory = await getTemporaryDirectory();
      final files = directory.listSync();
      int deletedCount = 0;
      int totalSize = 0;

      for (final file in files) {
        if (file.path.contains('cached_') && file.path.endsWith('.pdf')) {
          if (smartCleanup) {
            // حذف الملفات الأقدم من 7 أيام فقط
            final stat = await file.stat();
            final age = DateTime.now().difference(stat.modified);
            if (age.inDays > 7) {
              totalSize += stat.size;
              await file.delete();
              deletedCount++;
            }
          } else {
            // حذف جميع الملفات
            final stat = await file.stat();
            totalSize += stat.size;
            await file.delete();
            deletedCount++;
          }
        }
      }

      if (kDebugMode) {
        print('تم حذف $deletedCount ملف PDF (${_formatFileSize(totalSize)})');
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في مسح التخزين المؤقت: $e');
      }
    }
  }

  /// مسح الملفات المؤقتة القديمة تلقائياً
  static Future<void> autoCleanupOldFiles() async {
    try {
      final directory = await getTemporaryDirectory();
      final files = directory.listSync();

      for (final file in files) {
        if (file.path.contains('temp_pdf_')) {
          final stat = await file.stat();
          final age = DateTime.now().difference(stat.modified);
          // حذف الملفات المؤقتة الأقدم من ساعة واحدة
          if (age.inHours > 1) {
            await file.delete();
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تنظيف الملفات المؤقتة: $e');
      }
    }
  }
}

/// رمز إلغاء التحميل
class CancelToken {
  bool _isCancelled = false;

  bool get isCancelled => _isCancelled;

  void cancel() {
    _isCancelled = true;
  }
}

/// نتيجة تحميل PDF
class PdfLoadResult {
  final bool isSuccess;
  final bool isCancelled;
  final String? filePath;
  final String? error;

  PdfLoadResult._({
    required this.isSuccess,
    required this.isCancelled,
    this.filePath,
    this.error,
  });

  factory PdfLoadResult.success(String filePath) {
    return PdfLoadResult._(
      isSuccess: true,
      isCancelled: false,
      filePath: filePath,
    );
  }

  factory PdfLoadResult.error(String error) {
    return PdfLoadResult._(isSuccess: false, isCancelled: false, error: error);
  }

  factory PdfLoadResult.cancelled() {
    return PdfLoadResult._(isSuccess: false, isCancelled: true);
  }
}
