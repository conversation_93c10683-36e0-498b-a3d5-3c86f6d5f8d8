import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';

/// خدمة تحميل صفحات PDF بشكل تدريجي للملفات الكبيرة
class PdfPageLoader {
  static final Map<String, PdfDocument> _documentCache = {};
  static final Map<String, List<Uint8List?>> _pageCache = {};
  static const int _maxCachedPages = 10; // عدد الصفحات المخزنة في الذاكرة

  /// تحميل مستند PDF مع تحسين الذاكرة
  static Future<PdfDocument?> loadDocument(String filePath) async {
    try {
      // التحقق من وجود المستند في Cache
      if (_documentCache.containsKey(filePath)) {
        return _documentCache[filePath];
      }

      // تحميل المستند
      final file = File(filePath);
      if (!await file.exists()) {
        return null;
      }

      final bytes = await file.readAsBytes();
      final document = PdfDocument(inputBytes: bytes);

      // حفظ في Cache مع تحديد حد أقصى
      if (_documentCache.length >= 3) {
        // إزالة أقدم مستند
        final oldestKey = _documentCache.keys.first;
        _documentCache[oldestKey]?.dispose();
        _documentCache.remove(oldestKey);
        _pageCache.remove(oldestKey);
      }

      _documentCache[filePath] = document;
      _pageCache[filePath] = List.filled(document.pages.count, null);

      if (kDebugMode) {
        print('📄 تم تحميل PDF: ${document.pages.count} صفحة');
      }

      return document;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل PDF: $e');
      }
      return null;
    }
  }

  /// تحميل صفحة محددة كصورة (للعرض السريع)
  static Future<Uint8List?> loadPageAsImage(
    String filePath,
    int pageIndex, {
    double dpi = 150, // جودة الصورة
  }) async {
    try {
      // التحقق من وجود الصفحة في Cache
      final pageCache = _pageCache[filePath];
      if (pageCache != null &&
          pageIndex < pageCache.length &&
          pageCache[pageIndex] != null) {
        return pageCache[pageIndex];
      }

      // تحميل المستند
      final document = await loadDocument(filePath);
      if (document == null || pageIndex >= document.pages.count) {
        return null;
      }

      // تحويل الصفحة إلى صورة باستخدام Syncfusion
      final page = document.pages[pageIndex];
      final imageBytes = await page.rasterizeToImage(dpi: dpi);

      // حفظ في Cache مع إدارة الذاكرة
      if (pageCache != null) {
        // إزالة صفحات قديمة إذا تجاوزنا الحد الأقصى
        final cachedCount = pageCache.where((p) => p != null).length;
        if (cachedCount >= _maxCachedPages) {
          // إزالة أقدم صفحة (أول صفحة محملة)
          for (int i = 0; i < pageCache.length; i++) {
            if (pageCache[i] != null) {
              pageCache[i] = null;
              break;
            }
          }
        }

        pageCache[pageIndex] = imageBytes;
      }

      if (kDebugMode) {
        print(
          '📄 تم تحميل الصفحة $pageIndex (${_formatBytes(imageBytes.length)})',
        );
      }

      return imageBytes;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل الصفحة $pageIndex: $e');
      }
      return null;
    }
  }

  /// تحميل مسبق للصفحات المجاورة (للتنقل السريع)
  static Future<void> preloadAdjacentPages(
    String filePath,
    int currentPage, {
    int range = 2, // عدد الصفحات المجاورة للتحميل
  }) async {
    try {
      final document = _documentCache[filePath];
      if (document == null) return;

      final totalPages = document.pages.count;

      // تحديد نطاق الصفحات للتحميل المسبق
      final startPage = (currentPage - range).clamp(0, totalPages - 1);
      final endPage = (currentPage + range).clamp(0, totalPages - 1);

      // تحميل الصفحات في الخلفية
      for (int i = startPage; i <= endPage; i++) {
        if (i != currentPage) {
          // تحميل بدون انتظار (في الخلفية)
          loadPageAsImage(filePath, i, dpi: 100); // جودة أقل للتحميل المسبق
        }
      }

      if (kDebugMode) {
        print('🔄 تحميل مسبق للصفحات $startPage-$endPage');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التحميل المسبق: $e');
      }
    }
  }

  /// الحصول على معلومات الصفحة بدون تحميل
  static Future<PageInfo?> getPageInfo(String filePath, int pageIndex) async {
    try {
      final document = await loadDocument(filePath);
      if (document == null || pageIndex >= document.pages.count) {
        return null;
      }

      final page = document.pages[pageIndex];
      return PageInfo(
        width: page.size.width,
        height: page.size.height,
        pageNumber: pageIndex + 1,
        totalPages: document.pages.count,
      );
    } catch (e) {
      return null;
    }
  }

  /// مسح Cache لتوفير الذاكرة
  static void clearCache({String? specificFile}) {
    try {
      if (specificFile != null) {
        // مسح ملف محدد
        _documentCache[specificFile]?.dispose();
        _documentCache.remove(specificFile);
        _pageCache.remove(specificFile);
      } else {
        // مسح جميع الملفات
        for (final document in _documentCache.values) {
          document.dispose();
        }
        _documentCache.clear();
        _pageCache.clear();
      }

      if (kDebugMode) {
        print('🧹 تم مسح PDF Cache');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في مسح Cache: $e');
      }
    }
  }

  /// تحسين الذاكرة
  static void optimizeMemory() {
    try {
      // إزالة الصفحات المخزنة مؤقتاً
      for (final pageList in _pageCache.values) {
        for (int i = 0; i < pageList.length; i++) {
          pageList[i] = null;
        }
      }

      if (kDebugMode) {
        print('🧠 تم تحسين ذاكرة PDF');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحسين الذاكرة: $e');
      }
    }
  }

  /// تنسيق حجم البايتات
  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  /// الحصول على إحصائيات Cache
  static CacheStats getCacheStats() {
    int totalDocuments = _documentCache.length;
    int totalPages = 0;
    int cachedPages = 0;

    for (final pageList in _pageCache.values) {
      totalPages += pageList.length;
      cachedPages += pageList.where((p) => p != null).length;
    }

    return CacheStats(
      documentsInCache: totalDocuments,
      totalPages: totalPages,
      cachedPages: cachedPages,
      memoryUsage: _estimateMemoryUsage(),
    );
  }

  /// تقدير استخدام الذاكرة
  static String _estimateMemoryUsage() {
    int totalBytes = 0;

    for (final pageList in _pageCache.values) {
      for (final page in pageList) {
        if (page != null) {
          totalBytes += page.length;
        }
      }
    }

    return _formatBytes(totalBytes);
  }
}

/// معلومات الصفحة
class PageInfo {
  final double width;
  final double height;
  final int pageNumber;
  final int totalPages;

  PageInfo({
    required this.width,
    required this.height,
    required this.pageNumber,
    required this.totalPages,
  });

  double get aspectRatio => width / height;
}

/// إحصائيات Cache
class CacheStats {
  final int documentsInCache;
  final int totalPages;
  final int cachedPages;
  final String memoryUsage;

  CacheStats({
    required this.documentsInCache,
    required this.totalPages,
    required this.cachedPages,
    required this.memoryUsage,
  });

  @override
  String toString() {
    return 'PDF Cache: $documentsInCache docs, $cachedPages/$totalPages pages, $memoryUsage';
  }
}
