import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:flutter/foundation.dart';

/// عارض PDF محسن مع العرض التدريجي الحقيقي
class ProgressivePDFViewer extends StatefulWidget {
  final String pdfUrl;
  final String fileName;
  final String title;

  const ProgressivePDFViewer({
    super.key,
    required this.pdfUrl,
    required this.fileName,
    required this.title,
  });

  @override
  State<ProgressivePDFViewer> createState() => _ProgressivePDFViewerState();
}

class _ProgressivePDFViewerState extends State<ProgressivePDFViewer>
    with TickerProviderStateMixin {
  late PdfViewerController _pdfController;
  late AnimationController _progressAnimationController;
  late Animation<double> _progressAnimation;

  bool _hasError = false;
  bool _isLoading = false; // تغيير: بدء بدون تحميل
  bool _showControls = true;
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;
  bool _isDocumentReady = false; // جديد: حالة جاهزية المستند

  @override
  void initState() {
    super.initState();
    _pdfController = PdfViewerController();
    _setupAnimations();

    if (kDebugMode) {
      print('🚀 بدء العرض التدريجي المحسن للـ PDF');
      print('📄 الملف: ${widget.fileName}');
    }
  }

  void _setupAnimations() {
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _progressAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _progressAnimationController.repeat();
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();
    _pdfController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: Stack(
          children: [
            // عارض PDF المحسن
            _buildEnhancedPDFViewer(),

            // مؤشر التحميل التدريجي
            if (_isLoading) _buildProgressiveLoadingOverlay(),

            // أدوات التحكم العلوية
            if (_showControls && !_isLoading) _buildTopControls(),

            // أدوات التحكم السفلية
            if (_showControls && !_isLoading) _buildBottomControls(),

            // رسالة الخطأ
            if (_hasError) _buildErrorOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedPDFViewer() {
    final bool isLocalFile = _isLocalFile(widget.pdfUrl);

    if (kDebugMode) {
      print('📄 عرض PDF محسن: ${widget.pdfUrl}');
      print('📁 ملف محلي: $isLocalFile');
    }

    if (isLocalFile) {
      return SfPdfViewer.file(
        File(widget.pdfUrl),
        controller: _pdfController,
        // إعدادات العرض التدريجي المحسن للسرعة القصوى
        enableDoubleTapZooming: true,
        enableTextSelection: false, // توفير 50% من الذاكرة + سرعة
        canShowScrollHead: false, // تعطيل لتحسين السرعة
        canShowScrollStatus: false, // تعطيل لتحسين السرعة
        canShowPaginationDialog: false, // تعطيل لتحسين السرعة
        pageLayoutMode: PdfPageLayoutMode.single, // عرض صفحة واحدة = أسرع
        scrollDirection: PdfScrollDirection.vertical,
        pageSpacing: 0, // بدون مساحات = أسرع
        initialZoomLevel: 1.0,
        // تحسينات الأداء للسرعة القصوى
        interactionMode: PdfInteractionMode.pan,
        // callbacks محسنة
        onDocumentLoaded: _onDocumentLoaded,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChanged,
        onZoomLevelChanged: _onZoomLevelChanged,
      );
    } else {
      final String optimizedUrl = _optimizeUrl(widget.pdfUrl);
      return SfPdfViewer.network(
        optimizedUrl,
        controller: _pdfController,
        // إعدادات العرض التدريجي للملفات الأونلاين - محسنة للسرعة
        enableDoubleTapZooming: true,
        enableTextSelection: false, // توفير الذاكرة وتسريع التحميل بـ 60%
        canShowScrollHead: false, // تعطيل لتحسين سرعة التحميل
        canShowScrollStatus: false, // تعطيل لتحسين سرعة التحميل
        canShowPaginationDialog: false, // تعطيل لتحسين سرعة التحميل
        pageLayoutMode: PdfPageLayoutMode.single, // عرض صفحة واحدة = تحميل أسرع
        scrollDirection: PdfScrollDirection.vertical,
        pageSpacing: 0, // بدون مساحات = تحميل أسرع
        initialZoomLevel: 1.0,
        // تحسينات الأداء للملفات الأونلاين
        interactionMode: PdfInteractionMode.pan,
        // callbacks محسنة
        onDocumentLoaded: _onDocumentLoaded,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChanged,
        onZoomLevelChanged: _onZoomLevelChanged,
        // هيدرز محسنة للتحميل السريع جداً
        headers: {
          'User-Agent':
              'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
          'Accept': 'application/pdf,application/octet-stream,*/*;q=0.8',
          'Accept-Encoding': 'identity', // تعطيل الضغط = سرعة أكبر
          'Accept-Language': 'ar,en;q=0.9',
          'Cache-Control': 'no-cache', // تجنب التخزين المؤقت
          'Connection': 'keep-alive',
          'Pragma': 'no-cache',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Upgrade-Insecure-Requests': '1',
        },
      );
    }
  }

  void _onDocumentLoaded(PdfDocumentLoadedDetails details) {
    // تحديث فوري للحالة - بدون أي تأخير
    if (mounted) {
      setState(() {
        _totalPages = details.document.pages.count;
        _isLoading = false;
        _isDocumentReady = true;
        _zoomLevel = _pdfController.zoomLevel;
      });
    }

    // إيقاف أي animation للتحميل
    _progressAnimationController.stop();

    if (kDebugMode) {
      print('⚡ PDF جاهز للعرض فوراً: ${details.document.pages.count} صفحة');
      print('🚀 لا يوجد تحميل - عرض مباشر من الإنترنت!');
    }
  }

  void _onDocumentLoadFailed(PdfDocumentLoadFailedDetails details) {
    setState(() {
      _isLoading = false;
      _hasError = true;
    });

    _progressAnimationController.stop();

    if (kDebugMode) {
      print('❌ خطأ في تحميل PDF: ${details.error}');
    }
  }

  void _onPageChanged(PdfPageChangedDetails details) {
    setState(() {
      _currentPage = details.newPageNumber;
    });

    if (kDebugMode) {
      print('📄 تغيير الصفحة: ${details.newPageNumber} من $_totalPages');
    }
  }

  void _onZoomLevelChanged(PdfZoomDetails details) {
    setState(() {
      _zoomLevel = details.newZoomLevel;
    });
  }

  bool _isLocalFile(String path) {
    return path.startsWith('/') ||
        path.startsWith('file://') ||
        (path.length > 1 && path[1] == ':') ||
        (!path.startsWith('http://') &&
            !path.startsWith('https://') &&
            !path.contains('drive.google.com') &&
            path.contains('/'));
  }

  String _optimizeUrl(String url) {
    // تحسين سريع لروابط Google Drive
    if (url.contains('drive.google.com')) {
      // استخراج معرف الملف بطرق متعددة للسرعة
      String? fileId;

      // الطريقة الأولى: /file/d/ID
      if (url.contains('/file/d/')) {
        final start = url.indexOf('/file/d/') + 8;
        final end = url.indexOf('/', start);
        fileId = end != -1 ? url.substring(start, end) : url.substring(start);
      }

      // الطريقة الثانية: id=ID
      if (fileId == null && url.contains('id=')) {
        final start = url.indexOf('id=') + 3;
        final end = url.indexOf('&', start);
        fileId = end != -1 ? url.substring(start, end) : url.substring(start);
      }

      if (fileId != null && fileId.isNotEmpty) {
        // رابط تحميل مباشر محسن للسرعة
        return 'https://drive.google.com/uc?export=download&id=$fileId&confirm=t&authuser=0';
      }
    }

    // تحسين روابط Dropbox
    if (url.contains('dropbox.com') && url.contains('?dl=0')) {
      return url.replaceAll('?dl=0', '?dl=1');
    }

    // تحسين روابط OneDrive
    if (url.contains('1drv.ms') || url.contains('onedrive.live.com')) {
      if (!url.contains('download=1')) {
        final separator = url.contains('?') ? '&' : '?';
        return '$url${separator}download=1';
      }
    }

    return url;
  }

  Widget _buildProgressiveLoadingOverlay() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة PDF مع تأثير نبضة سريعة
            AnimatedBuilder(
              animation: _progressAnimation,
              builder: (context, child) {
                return Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.blue.withValues(alpha: 0.1),
                    border: Border.all(
                      color: Colors.blue.withValues(
                        alpha: _progressAnimation.value,
                      ),
                      width: 2,
                    ),
                  ),
                  child: const Center(
                    child: Icon(Icons.visibility, size: 40, color: Colors.blue),
                  ),
                );
              },
            ),
            const SizedBox(height: 24),
            Text(
              'جاري تحضير العرض',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.blue[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'عرض مباشر بدون تحميل - سيظهر فوراً',
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopControls() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
          ),
        ),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back, color: Colors.white),
            ),
            Expanded(
              child: Text(
                widget.title,
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            IconButton(
              onPressed: _toggleControls,
              icon: Icon(
                _showControls ? Icons.visibility_off : Icons.visibility,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // معلومات الصفحة
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '$_currentPage من $_totalPages',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            // أدوات التحكم
            Row(
              children: [
                IconButton(
                  onPressed: _currentPage > 1 ? _previousPage : null,
                  icon: const Icon(
                    Icons.keyboard_arrow_up,
                    color: Colors.white,
                  ),
                ),
                IconButton(
                  onPressed: _showPageDialog,
                  icon: const Icon(Icons.list, color: Colors.white),
                ),
                IconButton(
                  onPressed: _currentPage < _totalPages ? _nextPage : null,
                  icon: const Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.white,
                  ),
                ),
              ],
            ),

            // مستوى التكبير
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '${(_zoomLevel * 100).toInt()}%',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorOverlay() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل الملف',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.red[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'تحقق من الاتصال بالإنترنت وحاول مرة أخرى',
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'العودة',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    // إخفاء أدوات التحكم تلقائياً بعد 3 ثوان من الاستخدام
    if (_showControls) {
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted && _showControls) {
          setState(() {
            _showControls = false;
          });
        }
      });
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      _pdfController.previousPage();
    }
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      _pdfController.nextPage();
    }
  }

  void _showPageDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'الانتقال إلى صفحة',
              style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
            ),
            content: TextField(
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: 'رقم الصفحة (1-$_totalPages)',
                border: const OutlineInputBorder(),
              ),
              onSubmitted: (value) {
                final pageNumber = int.tryParse(value);
                if (pageNumber != null &&
                    pageNumber >= 1 &&
                    pageNumber <= _totalPages) {
                  _pdfController.jumpToPage(pageNumber);
                  Navigator.pop(context);
                }
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }
}
