import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// خدمة محسنة لإدارة ملفات PDF مع دعم Google Drive المباشر
class EnhancedPDFService {
  static final Dio _dio = Dio();
  static final Map<String, String> _urlCache = {};

  /// تحويل رابط إلى رابط مباشر محسن للعرض التدريجي السريع
  static String convertToDirectUrl(String originalUrl) {
    if (kDebugMode) {
      print('🔗 تحويل رابط للعرض التدريجي السريع: $originalUrl');
    }

    // إذا كان الرابط محفوظ في الكاش
    if (_urlCache.containsKey(originalUrl)) {
      final cachedUrl = _urlCache[originalUrl]!;
      if (kDebugMode) {
        print('📦 استخدام رابط من الكاش: $cachedUrl');
      }
      return cachedUrl;
    }

    String directUrl = originalUrl;

    // تحويل روابط Google Drive المختلفة إلى صيغة مباشرة
    if (originalUrl.contains('drive.google.com')) {
      // استخراج معرف الملف من الرابط
      String? fileId;

      // صيغة: https://drive.google.com/file/d/FILE_ID/view
      if (originalUrl.contains('/file/d/')) {
        final regex = RegExp(r'/file/d/([a-zA-Z0-9_-]+)');
        final match = regex.firstMatch(originalUrl);
        fileId = match?.group(1);
      }
      // صيغة: https://drive.google.com/open?id=FILE_ID
      else if (originalUrl.contains('open?id=')) {
        final regex = RegExp(r'id=([a-zA-Z0-9_-]+)');
        final match = regex.firstMatch(originalUrl);
        fileId = match?.group(1);
      }
      // صيغة: https://drive.google.com/uc?id=FILE_ID
      else if (originalUrl.contains('uc?id=')) {
        final regex = RegExp(r'id=([a-zA-Z0-9_-]+)');
        final match = regex.firstMatch(originalUrl);
        fileId = match?.group(1);
      }

      if (fileId != null) {
        // إنشاء رابط مباشر للعرض التدريجي السريع
        directUrl =
            'https://drive.google.com/uc?export=view&id=$fileId&confirm=t';

        if (kDebugMode) {
          print('✅ تم تحويل رابط Google Drive للعرض التدريجي السريع');
          print('📄 معرف الملف: $fileId');
          print('🔗 الرابط المحسن: $directUrl');
        }
      }
    }
    // تحويل روابط Dropbox للعرض المباشر
    else if (originalUrl.contains('dropbox.com') &&
        originalUrl.contains('?dl=0')) {
      directUrl = originalUrl.replaceAll('?dl=0', '?dl=1&raw=1');

      if (kDebugMode) {
        print('✅ تم تحويل رابط Dropbox للعرض المباشر');
        print('🔗 الرابط المحسن: $directUrl');
      }
    }
    // تحويل روابط OneDrive للعرض المباشر
    else if (originalUrl.contains('1drv.ms') ||
        originalUrl.contains('onedrive.live.com')) {
      directUrl =
          originalUrl.contains('?')
              ? '$originalUrl&download=1'
              : '$originalUrl?download=1';

      if (kDebugMode) {
        print('✅ تم تحويل رابط OneDrive للعرض المباشر');
        print('🔗 الرابط المحسن: $directUrl');
      }
    }

    // حفظ في الكاش
    _urlCache[originalUrl] = directUrl;

    if (kDebugMode) {
      print('💾 تم حفظ الرابط في الكاش للاستخدام السريع');
    }

    return directUrl;
  }

  /// التحقق من حالة الاتصال بالإنترنت
  static Future<bool> isConnected() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return !connectivityResult.contains(ConnectivityResult.none);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص الاتصال: $e');
      }
      return false;
    }
  }

  /// تحميل PDF إلى التخزين المحلي مع تتبع تقدم محسن
  static Future<String?> downloadPDFToLocal({
    required String url,
    required String fileName,
    Function(double progress)? onProgress,
    Function(int received, int total)? onReceiveProgress,
    Function(String error)? onError,
  }) async {
    try {
      if (kDebugMode) {
        print('📥 بدء تحميل PDF: $fileName');
        print('🔗 من الرابط: $url');
      }

      // التأكد من الاتصال بالإنترنت
      if (!await isConnected()) {
        throw Exception('لا يوجد اتصال بالإنترنت');
      }

      // الحصول على مجلد التخزين الداخلي للتطبيق
      final appDir = await getApplicationDocumentsDirectory();
      final pdfDir = Directory('${appDir.path}/pdfs');

      // إنشاء مجلد PDFs إذا لم يكن موجوداً
      if (!await pdfDir.exists()) {
        await pdfDir.create(recursive: true);
      }

      // تنظيف اسم الملف
      final cleanFileName = fileName.replaceAll(RegExp(r'[^\w\s-.]'), '');
      final filePath = '${pdfDir.path}/$cleanFileName.pdf';

      // التحقق من وجود الملف مسبقاً
      final file = File(filePath);
      if (await file.exists()) {
        final fileSize = await file.length();
        if (fileSize > 1024) {
          // التأكد من أن الملف ليس فارغاً
          if (kDebugMode) {
            print('✅ الملف موجود مسبقاً: $filePath');
          }
          return filePath;
        } else {
          // حذف الملف التالف
          await file.delete();
        }
      }

      // تحويل الرابط إلى رابط مباشر
      final directUrl = convertToDirectUrl(url);

      // تحميل الملف مع تتبع تقدم محسن
      await _dio.download(
        directUrl,
        filePath,
        onReceiveProgress: (received, total) {
          // استدعاء callback للبيانات الخام
          onReceiveProgress?.call(received, total);

          if (total != -1 && onProgress != null) {
            final progress = received / total;
            onProgress(progress);
            if (kDebugMode) {
              final receivedMB = (received / 1024 / 1024).toStringAsFixed(1);
              final totalMB = (total / 1024 / 1024).toStringAsFixed(1);
              print(
                '📊 تقدم التحميل: ${(progress * 100).toStringAsFixed(1)}% ($receivedMB/$totalMB MB)',
              );
            }
          }
        },
        options: Options(
          headers: {
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/pdf,*/*',
          },
          receiveTimeout: const Duration(minutes: 10),
          sendTimeout: const Duration(minutes: 5),
        ),
      );

      // التحقق من نجاح التحميل
      if (await file.exists()) {
        final fileSize = await file.length();
        if (fileSize > 1024) {
          if (kDebugMode) {
            print('✅ تم تحميل PDF بنجاح: $filePath');
            print(
              '📊 حجم الملف: ${(fileSize / 1024 / 1024).toStringAsFixed(2)} MB',
            );
          }
          return filePath;
        } else {
          // حذف الملف التالف
          await file.delete();
          throw Exception('الملف المحمل تالف أو فارغ');
        }
      } else {
        throw Exception('فشل في حفظ الملف');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل PDF: $e');
      }
      return null;
    }
  }

  /// الحصول على قائمة الملفات المحملة محلياً
  static Future<List<File>> getLocalPDFs() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final pdfDir = Directory('${appDir.path}/pdfs');

      if (!await pdfDir.exists()) {
        return [];
      }

      final files = await pdfDir.list().toList();
      final pdfFiles =
          files
              .whereType<File>()
              .where((file) => file.path.toLowerCase().endsWith('.pdf'))
              .toList();

      // ترتيب الملفات حسب تاريخ التعديل (الأحدث أولاً)
      pdfFiles.sort((a, b) {
        final aStat = a.statSync();
        final bStat = b.statSync();
        return bStat.modified.compareTo(aStat.modified);
      });

      if (kDebugMode) {
        print('📁 تم العثور على ${pdfFiles.length} ملف PDF محلي');
      }

      return pdfFiles;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب الملفات المحلية: $e');
      }
      return [];
    }
  }

  /// حذف ملف PDF محلي
  static Future<bool> deleteLocalPDF(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        if (kDebugMode) {
          print('🗑️ تم حذف الملف: $filePath');
        }
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف الملف: $e');
      }
      return false;
    }
  }

  /// التحقق من وجود ملف محلي
  static Future<bool> isFileDownloaded(String fileName) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final cleanFileName = fileName.replaceAll(RegExp(r'[^\w\s-.]'), '');
      final filePath = '${appDir.path}/pdfs/$cleanFileName.pdf';
      final file = File(filePath);

      if (await file.exists()) {
        final fileSize = await file.length();
        return fileSize > 1024; // التأكد من أن الملف ليس فارغاً
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على مسار الملف المحلي
  static Future<String?> getLocalFilePath(String fileName) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final cleanFileName = fileName.replaceAll(RegExp(r'[^\w\s-.]'), '');
      final filePath = '${appDir.path}/pdfs/$cleanFileName.pdf';
      final file = File(filePath);

      if (await file.exists()) {
        final fileSize = await file.length();
        if (fileSize > 1024) {
          return filePath;
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// تنظيف الكاش والملفات المؤقتة
  static Future<void> clearCache() async {
    try {
      _urlCache.clear();

      if (kDebugMode) {
        print('🧹 تم تنظيف الكاش بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تنظيف الكاش: $e');
      }
    }
  }
}
