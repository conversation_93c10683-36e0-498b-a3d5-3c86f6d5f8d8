import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:flutter/foundation.dart';

/// عارض PDF فوري بدون تحميل - عرض مباشر من الإنترنت
class InstantStreamPDFViewer extends StatefulWidget {
  final String pdfUrl;
  final String fileName;
  final String title;

  const InstantStreamPDFViewer({
    super.key,
    required this.pdfUrl,
    required this.fileName,
    required this.title,
  });

  @override
  State<InstantStreamPDFViewer> createState() => _InstantStreamPDFViewerState();
}

class _InstantStreamPDFViewerState extends State<InstantStreamPDFViewer>
    with TickerProviderStateMixin {
  late PdfViewerController _pdfController;
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsAnimation;

  bool _hasError = false;
  bool _isInitializing = true;
  bool _showControls = true;
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;

  @override
  void initState() {
    super.initState();
    _pdfController = PdfViewerController();
    _setupAnimations();
    _initializeViewer();
  }

  void _setupAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));
    _controlsAnimationController.forward();
  }

  void _initializeViewer() {
    // بدء العرض فوراً بدون أي تأخير
    setState(() {
      _isInitializing = false;
    });

    if (kDebugMode) {
      print('⚡ عارض PDF فوري - بدء العرض مباشرة');
      print('🔗 الرابط: ${widget.pdfUrl}');
    }
  }

  @override
  void dispose() {
    _controlsAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: Stack(
          children: [
            // عارض PDF الرئيسي
            _buildMainPDFViewer(),
            
            // أدوات التحكم العلوية
            if (_showControls) _buildTopControls(),
            
            // أدوات التحكم السفلية
            if (_showControls) _buildBottomControls(),
            
            // شاشة الخطأ
            if (_hasError) _buildErrorScreen(),
            
            // شاشة التحضير السريعة
            if (_isInitializing) _buildInitializingScreen(),
          ],
        ),
      ),
    );
  }

  Widget _buildMainPDFViewer() {
    return GestureDetector(
      onTap: _toggleControls,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        child: _buildPDFContent(),
      ),
    );
  }

  Widget _buildPDFContent() {
    final bool isLocalFile = _isLocalFile(widget.pdfUrl);
    
    if (isLocalFile) {
      return SfPdfViewer.file(
        File(widget.pdfUrl),
        controller: _pdfController,
        // إعدادات محسنة للملفات المحلية
        enableDoubleTapZooming: true,
        enableTextSelection: true,
        canShowScrollHead: true,
        canShowScrollStatus: true,
        canShowPaginationDialog: true,
        pageLayoutMode: PdfPageLayoutMode.continuous,
        scrollDirection: PdfScrollDirection.vertical,
        pageSpacing: 4,
        initialZoomLevel: 1.0,
        onDocumentLoaded: _onDocumentLoaded,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChanged,
        onZoomLevelChanged: _onZoomLevelChanged,
      );
    } else {
      final String optimizedUrl = _optimizeUrl(widget.pdfUrl);
      return SfPdfViewer.network(
        optimizedUrl,
        controller: _pdfController,
        // إعدادات محسنة للعرض المباشر من الإنترنت
        enableDoubleTapZooming: true,
        enableTextSelection: true,
        canShowScrollHead: true,
        canShowScrollStatus: true,
        canShowPaginationDialog: true,
        pageLayoutMode: PdfPageLayoutMode.continuous,
        scrollDirection: PdfScrollDirection.vertical,
        pageSpacing: 4,
        initialZoomLevel: 1.0,
        onDocumentLoaded: _onDocumentLoaded,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChanged,
        onZoomLevelChanged: _onZoomLevelChanged,
        // هيدرز محسنة للعرض السريع
        headers: {
          'User-Agent': 'Mozilla/5.0 (Linux; Android 11) AppleWebKit/537.36',
          'Accept': 'application/pdf,*/*;q=0.8',
          'Accept-Encoding': 'identity',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      );
    }
  }

  void _onDocumentLoaded(PdfDocumentLoadedDetails details) {
    if (mounted) {
      setState(() {
        _totalPages = details.document.pages.count;
        _isInitializing = false;
        _hasError = false;
      });
    }

    if (kDebugMode) {
      print('⚡ PDF جاهز للعرض: ${details.document.pages.count} صفحة');
    }
  }

  void _onDocumentLoadFailed(PdfDocumentLoadFailedDetails details) {
    if (mounted) {
      setState(() {
        _hasError = true;
        _isInitializing = false;
      });
    }

    if (kDebugMode) {
      print('❌ خطأ في تحميل PDF: ${details.error}');
    }
  }

  void _onPageChanged(PdfPageChangedDetails details) {
    if (mounted) {
      setState(() {
        _currentPage = details.newPageNumber;
      });
    }
  }

  void _onZoomLevelChanged(PdfZoomDetails details) {
    if (mounted) {
      setState(() {
        _zoomLevel = details.newZoomLevel;
      });
    }
  }

  bool _isLocalFile(String path) {
    return path.startsWith('/') ||
        path.startsWith('file://') ||
        (path.length > 1 && path[1] == ':') ||
        (!path.startsWith('http://') && !path.startsWith('https://'));
  }

  String _optimizeUrl(String url) {
    // تحسين روابط Google Drive
    if (url.contains('drive.google.com')) {
      final RegExp regExp = RegExp(r'/file/d/([a-zA-Z0-9-_]+)');
      final match = regExp.firstMatch(url);
      if (match != null) {
        final fileId = match.group(1);
        return 'https://drive.google.com/uc?export=view&id=$fileId';
      }
    }

    // تحسين روابط Dropbox
    if (url.contains('dropbox.com') && url.contains('?dl=0')) {
      return url.replaceAll('?dl=0', '?dl=1');
    }

    return url;
  }

  Widget _buildTopControls() {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Transform.translate(
            offset: Offset(0, -50 * (1 - _controlsAnimation.value)),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.7),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.title,
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    onPressed: _shareFile,
                    icon: const Icon(Icons.share, color: Colors.white),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomControls() {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Transform.translate(
            offset: Offset(0, 80 * (1 - _controlsAnimation.value)),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.7),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildControlButton(
                    icon: Icons.zoom_out,
                    onPressed: _zoomOut,
                    enabled: _zoomLevel > 0.5,
                  ),
                  _buildControlButton(
                    icon: Icons.navigate_before,
                    onPressed: _previousPage,
                    enabled: _currentPage > 1,
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '$_currentPage / $_totalPages',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  _buildControlButton(
                    icon: Icons.navigate_next,
                    onPressed: _nextPage,
                    enabled: _currentPage < _totalPages,
                  ),
                  _buildControlButton(
                    icon: Icons.zoom_in,
                    onPressed: _zoomIn,
                    enabled: _zoomLevel < 3.0,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required bool enabled,
  }) {
    return IconButton(
      onPressed: enabled ? onPressed : null,
      icon: Icon(
        icon,
        color: enabled ? Colors.white : Colors.white.withValues(alpha: 0.5),
      ),
    );
  }

  Widget _buildErrorScreen() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'خطأ في عرض الملف',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.red[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'تحقق من الاتصال بالإنترنت وحاول مرة أخرى',
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'العودة',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInitializingScreen() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.blue.withValues(alpha: 0.1),
              ),
              child: const Center(
                child: Icon(Icons.visibility, size: 30, color: Colors.blue),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'تحضير العرض',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.blue[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'عرض مباشر بدون تحميل',
              style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    
    if (_showControls) {
      _controlsAnimationController.forward();
      // إخفاء تلقائي بعد 4 ثوان
      Future.delayed(const Duration(seconds: 4), () {
        if (mounted && _showControls) {
          setState(() {
            _showControls = false;
          });
          _controlsAnimationController.reverse();
        }
      });
    } else {
      _controlsAnimationController.reverse();
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      _pdfController.previousPage();
    }
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      _pdfController.nextPage();
    }
  }

  void _zoomIn() {
    if (_zoomLevel < 3.0) {
      _pdfController.zoomLevel = (_zoomLevel + 0.25).clamp(0.5, 3.0);
    }
  }

  void _zoomOut() {
    if (_zoomLevel > 0.5) {
      _pdfController.zoomLevel = (_zoomLevel - 0.25).clamp(0.5, 3.0);
    }
  }

  void _shareFile() {
    // تنفيذ مشاركة الملف
    if (kDebugMode) {
      print('مشاركة الملف: ${widget.fileName}');
    }
  }
}
